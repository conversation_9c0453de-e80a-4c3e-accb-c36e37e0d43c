"""
PDF Attachment API
--------------------------------------------------------------------
A FastAPI application for embedding and extracting PDFs.

Usage:
    1. Run directly with Python:
       $ python main.py

    2. Or use the Docker image:
       $ docker pull islamo03/pdf-api
       $ docker run -p 8001:8001 islamo03/pdf-api

       Note: http://127.0.0.1:8001/docs for the API docs.(Swager) 
       
DockerHub: https://hub.docker.com/r/islamo03/pdf-api
Email: <EMAIL>
--------------------------------------------------------------------
"""

from fastapi import FastAPI, File, UploadFile, HTTPException, Request   
from fastapi.responses import StreamingResponse, HTMLResponse
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
import pikepdf
from pikepdf import AttachedFileSpec, PdfError
from io import BytesIO
import zipfile
from typing import List
import hashlib
import json
from pathlib import Path

app = FastAPI(                                                                          # Create FastAPI instance   
    title="PDF Attachment API",                                                         # API title
    description="API for embedding and extracting PDFs",                                # API description
    version="1.0.0",                                                                    # API version
)

# Set up static files & templates for my front-end
BASE_DIR = Path(__file__).resolve().parent                                              # Get the directory of the current file 
templates = Jinja2Templates(directory=str(BASE_DIR / "templates"))                      # Set up templates directory    
app.mount("/static", StaticFiles(directory=str(BASE_DIR / "static")), name="static")    # Mount static files directory

def compute_checksum(data: bytes) -> str:                                               # Compute SHA-256 checksum
    return hashlib.sha256(data).hexdigest()                                 

@app.post("/create_embedded_pdf")   
async def create_embedded_pdf(                                                          # Endpoint to embed files into a PDF
    main_pdf: UploadFile = File(..., description="The main PDF file"),                    
    embedded_pdfs: List[UploadFile] = File(..., description="Files to embed")      
):
    # 1) Validate & read main PDF
    if main_pdf.content_type != "application/pdf":                                      # Check if main PDF is a valid PDF
        raise HTTPException(400, "Main file must be a PDF")                             # Raise exception if not a valid PDF

    main_bytes = await main_pdf.read()                                                  # Read main PDF 
    main_checksum = compute_checksum(main_bytes)                                        # Compute checksum for main PDF

    try:
        # 2) Open PDF for modification
        pdf = pikepdf.Pdf.open(BytesIO(main_bytes))                                     # Open main PDF for modification
    except PdfError as e:                                                               # Check if main PDF is a valid PDF
        raise HTTPException(400, detail=f"Invalid PDF: {e}")                            # Raise exception if not a valid PDF

    embedded_checksums = []                                                             # List to store checksums of embedded files

    # 3) Loop through uploads
    for upload in embedded_pdfs:                                                        # Loop through each file to embed
        fn = upload.filename or "embedded_file"                                         # Get filename or use default name
        content = await upload.read()                                                   # Read file content
        embedded_checksums.append((fn, compute_checksum(content)))                      # Compute checksum for embedded file

        # 4) Build AttachedFileSpec with correct signature:
        spec = AttachedFileSpec(
            pdf,                  # <— the PDF object
            content,              # data bytes
            filename=fn,          # embedded filename
            mime_type=upload.content_type or "" # MIME type
        )
        pdf.attachments[fn] = spec                                                      # Add attachment to PDF

    # 5) Save modified PDF
    out_pdf = BytesIO()                                                                 # Create BytesIO object to save modified PDF
    pdf.save(out_pdf)                                                                   # Save modified PDF to BytesIO object
    out_pdf.seek(0)                                                                     # Reset pointer to the beginning of the BytesIO object
    result_bytes = out_pdf.read()                                                       # Read modified PDF bytes
    result_checksum = compute_checksum(result_bytes)                                    # Compute checksum for modified PDF

    # 6) Return with checksums in headers
    headers = { # Add checksums to headers
        "Content-Disposition": "attachment; filename=embedded.pdf",                     # Add filename to headers
        "X-Checksum-Main": main_checksum,                                               # Add main PDF checksum to headers
        "X-Checksum-Result": result_checksum,                                           # Add modified PDF checksum to headers
        "X-Checksum-Embedded": json.dumps(dict(embedded_checksums))                     # Add embedded files checksums to headers
    }
    return StreamingResponse(                                                           # Return modified PDF as streaming response
        BytesIO(result_bytes),                                                          # PDF bytes
        media_type="application/pdf",                                                   # Set media type to PDF
        headers=headers                                                                 # Add headers with checksums
    )

@app.post("/extract_embedded_pdf")  
async def extract_embedded_pdf(                                                         # Endpoint to extract embedded files from a PDF
    pdf_file: UploadFile = File(..., description="PDF with attachments")):  

    pdf_bytes = await pdf_file.read()                                                   # Read PDF bytes   
    original_checksum = compute_checksum(pdf_bytes)                                     # Compute checksum for original PDF 

    try:
        pdf = pikepdf.Pdf.open(BytesIO(pdf_bytes))                                      # Open PDF for modification
    except PdfError as e:    
        raise HTTPException(400, detail=f"Invalid PDF: {e}")                            # Raise exception if not a valid PDF

    if not pdf.attachments:                                                             # Check if PDF has attachments
        raise HTTPException(404, "No embedded attachments found")      

    # Build clean PDF (no attachments)
    cleaning = BytesIO()    
    with pikepdf.Pdf.open(BytesIO(pdf_bytes)) as clean_pdf: 
        clean_pdf.attachments.clear()   
        clean_pdf.save(cleaning)    
    clean_bytes = cleaning.getvalue()
    clean_checksum = compute_checksum(clean_bytes)

    # Zip up clean PDF + attachments
    zip_buf = BytesIO()                                                                 # Create BytesIO object to store zipped bytes   
    with zipfile.ZipFile(zip_buf, "w", zipfile.ZIP_DEFLATED) as zf:
        zf.writestr("main_document.pdf", clean_bytes)                                   # Write clean PDF bytes to zip file   
        checks = [("main_document.pdf", clean_checksum)]                                # Create list to store checksums   
        for name, spec in pdf.attachments.items():                                      # Loop through each attachment
            data = spec.get_file().read_bytes()                                         # Read attachment bytes
            cs = compute_checksum(data)                                                 # Compute checksum for attachment
            checks.append((name, cs))                                                   # Add checksum to list
            zf.writestr(name, data)                                                     # Write attachment bytes to zip file
    zip_bytes = zip_buf.getvalue()                                                      # Get zipped bytes
    zip_checksum = compute_checksum(zip_bytes)                                          # Compute checksum for zipped bytes

    headers = {                                                                         # Add checksums to headers
        "Content-Disposition": "attachment; filename=extracted_attachments.zip",        # Add filename to headers
        "X-Checksum-Original": original_checksum,                                       # Add original PDF checksum to headers
        "X-Checksum-Clean-PDF": clean_checksum,                                         # Add clean PDF checksum to headers
        "X-Checksum-Zip": zip_checksum,                                                 # Add zipped PDF checksum to headers
        "X-Checksum-Extracted": json.dumps(dict(checks))                                # Add extracted files checksums to headers
    }
    return StreamingResponse(                                                           # Return zipped PDF as streaming response
        BytesIO(zip_bytes),                                                             # Zipped bytes
        media_type="application/zip",                                                   # Set media type to ZIP
        headers=headers                                                                 # Add headers with checksums
    )

@app.get("/health") 
def health_check():                                                                    # Endpoint to check API health
    return {"status": "healthy", "version": app.version}    

@app.get("/", response_class=HTMLResponse, include_in_schema=False) 
def home(request: Request):                                                            # Endpoint to serve the home page
    from datetime import datetime
    return templates.TemplateResponse("index.html", {"request": request, "now": datetime.now()})    

if __name__ == "__main__":                                                             # Run the app
    import uvicorn
    uvicorn.run(app, host="127.0.0.1", port=8001)   
