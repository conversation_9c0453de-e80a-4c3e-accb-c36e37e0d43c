body {
    background-color: #f8f9fa;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    transition: background-color 0.3s ease, color 0.3s ease;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

body.dark-mode {
    background-color: #212529;
    color: #f8f9fa;
}

body.dark-mode .card {
    background-color: #343a40;
    color: #f8f9fa;
}

body.dark-mode .card-header {
    border-bottom-color: #495057;
}

body.dark-mode .form-control {
    background-color: #343a40;
    color: #f8f9fa;
    border-color: #495057;
}

body.dark-mode .alert-light,
body.dark-mode .bg-light {
    background-color: #343a40 !important;
    color: #f8f9fa;
    border-color: #495057;
}

body.dark-mode .text-muted {
    color: #adb5bd !important;
}

body.dark-mode .form-text,
body.dark-mode .form-label,
body.dark-mode small,
body.dark-mode .form-check-label {
    color: #adb5bd !important;
}

body.dark-mode .form-switch {
    background-color: rgba(255,255,255,0.1) !important;
}

.container {
    max-width: 1200px;
    flex: 1;
}

.card {
    border-radius: 12px;
    box-shadow: 0 6px 15px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    border: none;
    overflow: hidden;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 20px rgba(0, 0, 0, 0.1);
}

.card-header {
    border-radius: 12px 12px 0 0 !important;
    font-weight: 600;
    padding: 1rem 1.5rem;
}

.btn {
    border-radius: 6px;
    font-weight: 500;
    padding: 10px 20px;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.btn-lg {
    padding: 12px 24px;
    font-size: 1.1rem;
}

.form-control {
    border-radius: 6px;
    padding: 12px;
    border: 1px solid #dee2e6;
    transition: all 0.3s ease;
}

.form-control:focus {
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
    border-color: #86b7fe;
}

.alert {
    border-radius: 8px;
    border: none;
}

footer {
    margin-top: auto;
    padding: 2rem 0;
}

/* Social links styles */
.social-links a {
    transition: all 0.3s ease;
    border-radius: 20px;
    padding: 6px 12px;
}

.social-links a:hover {
    transform: translateY(-3px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.social-links a:active {
    transform: translateY(0);
}

/* GitHub button */
.social-links a:nth-child(1):hover {
    background-color: #24292e;
    color: white;
    border-color: #24292e;
}

/* DockerHub button */
.social-links a:nth-child(2):hover {
    background-color: #2496ed;
    color: white;
    border-color: #2496ed;
}

/* Linktree button */
.social-links a:nth-child(3):hover {
    background-color: #43e660;
    color: white;
    border-color: #43e660;
}

body.dark-mode .social-links a {
    color: #adb5bd;
    border-color: #495057;
}

body.dark-mode .social-links a:hover {
    color: white;
}

/* Animation for status */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.fade-in {
    animation: fadeIn 0.5s ease-in-out;
}

/* Success and error indicators */
.success-icon {
    color: #198754;
}

.error-icon {
    color: #dc3545;
}

/* Drag and drop styles */
.drop-zone {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 140px;
    padding: 25px;
    border: 2px dashed #adb5bd;
    border-radius: 10px;
    background-color: #f8f9fa;
    transition: all 0.3s ease;
    cursor: pointer;
    text-align: center;
    overflow: hidden;
}

.drop-zone:hover,
.drop-zone.drop-zone--over {
    border-color: #0d6efd;
    background-color: rgba(13, 110, 253, 0.05);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
}

.drop-zone__input {
    /* Use Bootstrap's visually-hidden class instead of positioning */
    /* This prevents the input from capturing clicks but still allows it to be programmatically clicked */
}

/* Add Bootstrap's visually-hidden class if not already included */
.visually-hidden {
    position: absolute !important;
    width: 1px !important;
    height: 1px !important;
    padding: 0 !important;
    margin: -1px !important;
    overflow: hidden !important;
    clip: rect(0, 0, 0, 0) !important;
    white-space: nowrap !important;
    border: 0 !important;
}

.drop-zone__prompt {
    color: #6c757d;
    font-size: 1.1rem;
    transition: color 0.3s ease;
    max-width: 80%;
    line-height: 1.5;
}

.drop-zone:hover .drop-zone__prompt,
.drop-zone.drop-zone--over .drop-zone__prompt {
    color: #0d6efd;
}

body.dark-mode .drop-zone {
    background-color: #343a40;
    border-color: #495057;
}

body.dark-mode .drop-zone:hover,
body.dark-mode .drop-zone.drop-zone--over {
    border-color: #0d6efd;
    background-color: rgba(13, 110, 253, 0.1);
}

body.dark-mode .drop-zone__prompt {
    color: #adb5bd;
}

/* File preview styles */
.file-preview {
    background-color: rgba(0, 0, 0, 0.03);
    border-radius: 8px;
    padding: 12px 15px;
    margin-top: 12px;
    transition: all 0.3s ease;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.file-preview:hover {
    background-color: rgba(0, 0, 0, 0.05);
}

body.dark-mode .file-preview {
    background-color: rgba(255, 255, 255, 0.05);
    border-color: rgba(255, 255, 255, 0.1);
}

body.dark-mode .file-preview:hover {
    background-color: rgba(255, 255, 255, 0.08);
}

.file-preview .file-name {
    font-weight: 500;
    word-break: break-all;
}

.files-list > div {
    margin-bottom: 8px;
    padding-bottom: 8px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.files-list > div:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
}

body.dark-mode .files-list > div {
    border-bottom-color: rgba(255, 255, 255, 0.1);
}

/* Remove file button styles */
.remove-file {
    font-size: 0.8rem;
    opacity: 0.7;
    transition: opacity 0.2s ease, transform 0.2s ease;
}

.remove-file:hover {
    opacity: 1;
    transform: scale(1.1);
}

body.dark-mode .remove-file {
    filter: invert(1);
}

/* Checksum styles */
.text-monospace {
    font-family: SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
    font-size: 0.85rem;
}

.text-break {
    word-break: break-all !important;
    word-wrap: break-word !important;
}

#checksumDetails {
    background-color: rgba(0, 0, 0, 0.03);
    border-radius: 5px;
    padding: 10px;
}

body.dark-mode #checksumDetails {
    background-color: rgba(255, 255, 255, 0.05);
}

/* Download button styles */
#downloadButtonContainer {
    margin: 15px 0;
}

#downloadButton {
    padding: 10px 20px;
    font-weight: 500;
    transition: all 0.3s ease;
    min-width: 200px;
}

#downloadButton:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

body.dark-mode #downloadButton {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}
