# 📄 PDF Attachment Tool

<div align="center">

![Version](https://img.shields.io/badge/version-1.0.0-blue)
![Python](https://img.shields.io/badge/Python-3.12-blue?logo=python)
![FastAPI](https://img.shields.io/badge/FastAPI-0.104.1-009688?logo=fastapi)
![Docker](https://img.shields.io/badge/Docker-Ready-2496ED?logo=docker)
![License](https://img.shields.io/badge/license-MIT-green)

</div>

A modern web application for securely embedding files into PDFs and extracting attachments from PDFs with integrity verification.

<div align="center">

![PDF Attachment Tool](https://github.com/islambrg/PDF_API/blob/main/static/img/screenshot.png)

</div>

## ✨ Features

- **📎 Embed Files**: Securely embed any type of file into a PDF document
- **📦 Extract Files**: Extract attachments from PDFs with embedded files
- **🔐 Integrity Verification**: SHA-256 checksums for all files to verify integrity
- **🖱️ Drag & Drop Interface**: Easy file selection with intuitive drag and drop support
- **🌓 Dark/Light Mode**: Toggle between dark and light themes for comfortable viewing
- **🐳 Docker Support**: Run the application in a container for easy deployment
- **📱 Responsive Design**: Works on desktop and mobile devices
- **🔍 File Preview**: Preview files before embedding or extracting

## 🛠️ Technologies Used

- **Backend**:
  - FastAPI - High-performance Python web framework
  - pikepdf - PDF manipulation library
  - Python 3.12 - Latest Python features
  - Uvicorn - ASGI server

- **Frontend**:
  - HTML5, CSS3, JavaScript
  - Bootstrap 5 - Responsive design framework
  - Font Awesome - Icon library

- **Containerization**:
  - Docker - Application containerization
  - Docker Compose - Multi-container orchestration

## 🚀 Quick Start

### Using Docker (Recommended)

```bash
# Pull the image directly from DockerHub
docker pull islamo03/pdf-api

# Run the container
docker run -p 8001:8001 islamo03/pdf-api
```

Or clone and build:

```bash
# Clone the repository
git clone https://github.com/islambrg/PDF_API.git
cd PDF_API

# Start the application with Docker Compose
docker-compose up -d
```

The application will be available at http://localhost:8001

### Manual Installation

```bash
# Clone the repository
git clone https://github.com/islambrg/PDF_API.git
cd PDF_API

# Create a virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt

# Run the application
python main.py
```

The application will be available at http://localhost:8001

## 🔌 API Endpoints

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/create_embedded_pdf` | POST | Embed files into a PDF |
| `/extract_embedded_pdf` | POST | Extract attachments from a PDF |
| `/health` | GET | Health check endpoint |
| `/docs` | GET | API documentation (Swagger UI) |

## 📖 Usage Guide

### Embedding Files

1. Upload a main PDF file using the drag & drop area or file browser
2. Select one or more files to embed into the PDF
3. Click "Create Embedded PDF" button
4. View file checksums in the status area for integrity verification
5. Download the resulting PDF with embedded files

### Extracting Files

1. Upload a PDF file with attachments using the drag & drop area
2. Click "Extract Attachments" button
3. View file checksums in the status area for integrity verification
4. Download the ZIP file containing the extracted files

## 🔒 Security Features

- **File Integrity**: SHA-256 checksums for all files to verify integrity
- **Secure Processing**: All operations performed locally in the browser
- **No Data Storage**: Files are not stored on the server
- **Containerization**: Isolated environment for enhanced security
- **Health Monitoring**: Container health checks for reliability

## 💻 Development

### Prerequisites

- Python 3.12+
- Docker and Docker Compose (for containerization)
- Git

### Setting Up Development Environment

```bash
# Clone the repository
git clone https://github.com/islambrg/PDF_API.git
cd PDF_API

# Create a virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt

# Run the application in development mode
python main.py
```

### Running Tests

```bash
pytest
```

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 👨‍💻 Author

**Boureghda Mohamed Islam**

<div align="center">
  <a href="https://github.com/islambrg" target="_blank">
    <img src="https://img.shields.io/badge/GitHub-islambrg-181717?style=for-the-badge&logo=github" alt="GitHub">
  </a>
  <a href="https://hub.docker.com/u/islamo03" target="_blank">
    <img src="https://img.shields.io/badge/DockerHub-islamo03-2496ED?style=for-the-badge&logo=docker" alt="DockerHub">
  </a>
  <a href="https://linktr.ee/islambrg" target="_blank">
    <img src="https://img.shields.io/badge/Linktree-islambrg-43e660?style=for-the-badge&logo=linktree" alt="Linktree">
  </a>
</div>
