<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PDF Attachment Tool</title>
    <link rel="icon"
        href="data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 viewBox=%220 0 100 100%22><text y=%22.9em%22 font-size=%2290%22>📄</text></svg>">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="{{ url_for('static', path='/css/styles.css') }}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>

<body>
    <div class="container">
        <div class="d-flex justify-content-end mt-3">
            <div class="form-check form-switch p-2 rounded" style="background-color: rgba(0,0,0,0.05);">
                <input class="form-check-input" type="checkbox" id="darkModeToggle">
                <label class="form-check-label" for="darkModeToggle">
                    <i class="fas fa-moon"></i> Dark Mode
                </label>
            </div>
        </div>
        <header class="text-center my-5">
            <div class="mb-4">
                <i class="fas fa-file-pdf text-danger fa-3x mb-3"></i>
                <h1 class="display-4 fw-bold">PDF Attachment Tool</h1>
                <p class="lead fs-4">Securely embed files into PDFs or extract attachments</p>
            </div>
            <div class="row justify-content-center">
                <div class="col-md-10 col-lg-8">
                    <div class="alert alert-light border shadow-sm p-4">
                        <div class="d-flex">
                            <div class="me-3">
                                <i class="fas fa-info-circle text-primary fa-2x"></i>
                            </div>
                            <div class="text-start">
                                <h5 class="mb-2">About This Tool</h5>
                                <p class="mb-0">This tool allows you to embed any type of file into a PDF document and extract files from PDFs with attachments. All operations are performed securely in your browser, and checksums are provided to verify file integrity.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </header>

        <div class="row justify-content-center mb-4">
            <div class="col-lg-10">
                <div class="row g-4">
                    <div class="col-md-6">
                        <div class="card h-100 shadow-sm border-0">
                            <div class="card-header bg-primary text-white py-3">
                                <h2 class="h5 mb-0 fw-bold"><i class="fas fa-file-import me-2"></i>Embed Files into PDF</h2>
                            </div>
                            <div class="card-body p-4">
                                <form id="embedForm" enctype="multipart/form-data">
                                    <div class="mb-4">
                                        <label for="mainPdf" class="form-label fw-medium">Main PDF File</label>
                                        <div class="drop-zone" id="mainPdfDropZone">
                                            <span class="drop-zone__prompt"><i class="fas fa-cloud-upload-alt me-2"></i>Drag & drop PDF here or click to browse</span>
                                            <input type="file" class="drop-zone__input visually-hidden" id="mainPdf" name="main_pdf"
                                                accept="application/pdf" required>
                                        </div>
                                        <div id="mainPdfPreview" class="file-preview mt-2 d-none">
                                            <div class="d-flex align-items-center">
                                                <i class="fas fa-file-pdf text-danger me-2 fs-4"></i>
                                                <span class="file-name"></span>
                                                <button type="button" class="btn-close ms-auto remove-file" aria-label="Remove file"></button>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="mb-4">
                                        <label for="embeddedFiles" class="form-label fw-medium">Files to Embed</label>
                                        <div class="drop-zone" id="embeddedFilesDropZone">
                                            <span class="drop-zone__prompt"><i class="fas fa-cloud-upload-alt me-2"></i>Drag & drop files here or click to browse</span>
                                            <input type="file" class="drop-zone__input visually-hidden" id="embeddedFiles" name="embedded_pdfs" multiple
                                                required>
                                        </div>
                                        <div id="embeddedFilesPreview" class="file-preview mt-2 d-none">
                                            <div class="files-list"></div>
                                        </div>
                                        <div class="form-text mt-2">Select one or more files to embed into the main PDF.</div>
                                    </div>
                                    <div class="d-grid mt-4">
                                        <button type="submit" class="btn btn-primary btn-lg">
                                            <i class="fas fa-file-export me-2"></i>Create Embedded PDF
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="card h-100 shadow-sm border-0">
                            <div class="card-header bg-success text-white py-3">
                                <h2 class="h5 mb-0 fw-bold"><i class="fas fa-file-export me-2"></i>Extract Files from PDF</h2>
                            </div>
                            <div class="card-body p-4">
                                <form id="extractForm" enctype="multipart/form-data">
                                    <div class="mb-4">
                                        <label for="pdfFile" class="form-label fw-medium">PDF File with Attachments</label>
                                        <div class="drop-zone" id="pdfFileDropZone">
                                            <span class="drop-zone__prompt"><i class="fas fa-cloud-upload-alt me-2"></i>Drag & drop PDF here or click to browse</span>
                                            <input type="file" class="drop-zone__input visually-hidden" id="pdfFile" name="pdf_file"
                                                accept="application/pdf" required>
                                        </div>
                                        <div id="pdfFilePreview" class="file-preview mt-2 d-none">
                                            <div class="d-flex align-items-center">
                                                <i class="fas fa-file-pdf text-danger me-2 fs-4"></i>
                                                <span class="file-name"></span>
                                                <button type="button" class="btn-close ms-auto remove-file" aria-label="Remove file"></button>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="form-text mb-4">
                                        <i class="fas fa-info-circle me-1"></i> The extracted files will include the main PDF document without any attachments.
                                    </div>
                                    <div class="d-grid mt-4">
                                        <button type="submit" class="btn btn-success btn-lg">
                                            <i class="fas fa-download me-2"></i>Extract Attachments
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div class="card shadow-sm border-0 mb-5">
                    <div class="card-header bg-info text-white py-3">
                        <h2 class="h5 mb-0 fw-bold"><i class="fas fa-info-circle me-2"></i>Operation Status</h2>
                    </div>
                    <div class="card-body p-4">
                        <div id="status" class="alert alert-info d-none">
                            <div class="d-flex align-items-center mb-3">
                                <div class="spinner-border me-3" role="status" id="statusSpinner">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                                <span id="statusMessage" class="fw-bold fs-5">Processing...</span>
                            </div>
                            <div id="downloadButtonContainer" class="my-4 text-center d-none">
                                <button id="downloadButton" class="btn btn-primary btn-lg px-4 py-2">
                                    <i class="fas fa-download me-2"></i>Download File
                                </button>
                            </div>
                            <div id="checksumDetails" class="mt-4 d-none">
                                <hr>
                                <div class="d-flex align-items-center mb-3">
                                    <i class="fas fa-fingerprint text-primary me-2 fs-4"></i>
                                    <h5 class="mb-0">File Checksums (SHA-256)</h5>
                                </div>
                                <div id="checksumContent" class="small bg-light p-3 rounded border"></div>
                            </div>
                        </div>
                        <div class="text-center text-muted p-4 d-flex flex-column align-items-center" id="statusPlaceholder">
                            <i class="fas fa-file-pdf fa-3x mb-3 text-muted"></i>
                            <p class="mb-0">Operation status and file checksums will appear here</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <footer class="text-center py-4 mt-4 border-top">
            <div class="mb-3">
                <i class="fas fa-file-pdf text-danger me-2"></i>
                <span class="fw-bold">PDF Attachment Tool</span>
            </div>
            <p class="text-muted mb-3">Developed by Boureghda Mohamed Islam</p>

            <div class="social-links mb-3">
                <a href="https://github.com/islambrg" class="btn btn-outline-secondary btn-sm mx-1" target="_blank" title="GitHub">
                    <i class="fab fa-github me-1"></i> GitHub
                </a>
                <a href="https://hub.docker.com/u/islamo03" class="btn btn-outline-secondary btn-sm mx-1" target="_blank" title="DockerHub">
                    <i class="fab fa-docker me-1"></i> DockerHub
                </a>
                <a href="https://linktr.ee/islambrg" class="btn btn-outline-secondary btn-sm mx-1" target="_blank" title="Linktree">
                    <i class="fas fa-link me-1"></i> Linktree
                </a>
            </div>

            <p class="text-muted small">&copy; {{ now.year }} All rights reserved</p>
        </footer>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script src="{{ url_for('static', path='/js/script.js') }}"></script>
</body>

</html>