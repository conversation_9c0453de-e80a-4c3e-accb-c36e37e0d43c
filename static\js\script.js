document.addEventListener('DOMContentLoaded', function() {
    const embedForm = document.getElementById('embedForm');
    const extractForm = document.getElementById('extractForm');
    const status = document.getElementById('status');
    const statusMessage = document.getElementById('statusMessage');
    const statusSpinner = document.getElementById('statusSpinner');
    const darkModeToggle = document.getElementById('darkModeToggle');
    const downloadButton = document.getElementById('downloadButton');
    const downloadButtonContainer = document.getElementById('downloadButtonContainer');

    // Variable to store download information
    let currentDownloadInfo = null;

    // Drag and drop elements
    const mainPdfDropZone = document.getElementById('mainPdfDropZone');
    const mainPdfInput = document.getElementById('mainPdf');
    const mainPdfPreview = document.getElementById('mainPdfPreview');

    const embeddedFilesDropZone = document.getElementById('embeddedFilesDropZone');
    const embeddedFilesInput = document.getElementById('embeddedFiles');
    const embeddedFilesPreview = document.getElementById('embeddedFilesPreview');

    const pdfFileDropZone = document.getElementById('pdfFileDropZone');
    const pdfFileInput = document.getElementById('pdfFile');
    const pdfFilePreview = document.getElementById('pdfFilePreview');

    // Check for saved dark mode preference
    if (localStorage.getItem('darkMode') === 'enabled') {
        document.body.classList.add('dark-mode');
        darkModeToggle.checked = true;
    }

    // Download button click handler
    downloadButton.addEventListener('click', function() {
        if (currentDownloadInfo) {
            const a = document.createElement('a');
            a.style.display = 'none';
            a.href = currentDownloadInfo.url;
            a.download = currentDownloadInfo.filename;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);

            // Optional: Add a visual feedback that download has started
            const originalText = downloadButton.innerHTML;
            downloadButton.innerHTML = '<i class="fas fa-check me-2"></i>Download Started';
            downloadButton.classList.remove('btn-primary');
            downloadButton.classList.add('btn-success');

            // Reset button after a delay
            setTimeout(() => {
                downloadButton.innerHTML = originalText;
                downloadButton.classList.remove('btn-success');
                downloadButton.classList.add('btn-primary');
            }, 2000);
        }
    });

    // Dark mode toggle
    darkModeToggle.addEventListener('change', function() {
        if (this.checked) {
            document.body.classList.add('dark-mode');
            localStorage.setItem('darkMode', 'enabled');
        } else {
            document.body.classList.remove('dark-mode');
            localStorage.setItem('darkMode', 'disabled');
        }
    });

    // Function to show status message
    function showStatus(message, isLoading = true, isError = false, checksums = null, downloadInfo = null) {
        // Hide placeholder and show status
        const statusPlaceholder = document.getElementById('statusPlaceholder');
        if (statusPlaceholder) {
            statusPlaceholder.classList.add('d-none');
        }

        status.classList.remove('d-none', 'alert-info', 'alert-success', 'alert-danger');

        if (isError) {
            status.classList.add('alert-danger');
        } else if (!isLoading) {
            status.classList.add('alert-success');
        } else {
            status.classList.add('alert-info');
        }

        statusMessage.textContent = message;

        if (isLoading) {
            statusSpinner.classList.remove('d-none');
            downloadButtonContainer.classList.add('d-none');
        } else {
            statusSpinner.classList.add('d-none');

            // Handle download button
            if (downloadInfo) {
                currentDownloadInfo = downloadInfo;
                downloadButtonContainer.classList.remove('d-none');

                // Update button text based on file type
                if (downloadInfo.fileType === 'pdf') {
                    downloadButton.innerHTML = '<i class="fas fa-file-pdf me-2"></i>Download PDF';
                } else if (downloadInfo.fileType === 'zip') {
                    downloadButton.innerHTML = '<i class="fas fa-file-archive me-2"></i>Download ZIP';
                } else {
                    downloadButton.innerHTML = '<i class="fas fa-download me-2"></i>Download File';
                }
            } else {
                downloadButtonContainer.classList.add('d-none');
            }
        }

        // Handle checksums display
        const checksumDetails = document.getElementById('checksumDetails');
        const checksumContent = document.getElementById('checksumContent');

        if (checksums) {
            // Format and display checksums
            let checksumHtml = '';

            // Process different types of checksums based on operation
            if (checksums.operation === 'embed') {
                checksumHtml += `<div class="mb-2">
                    <strong>Main PDF:</strong>
                    <span class="text-monospace text-break">${checksums.main}</span>
                </div>`;

                checksumHtml += `<div class="mb-2">
                    <strong>Result PDF:</strong>
                    <span class="text-monospace text-break">${checksums.result}</span>
                </div>`;

                if (checksums.embedded) {
                    checksumHtml += `<div class="mb-2"><strong>Embedded Files:</strong></div>`;
                    checksumHtml += `<ul class="list-unstyled ms-3">`;

                    for (const [filename, checksum] of Object.entries(checksums.embedded)) {
                        checksumHtml += `<li class="mb-1">
                            <i class="fas fa-file-alt me-1"></i> <strong>${filename}:</strong>
                            <span class="text-monospace text-break">${checksum}</span>
                        </li>`;
                    }

                    checksumHtml += `</ul>`;
                }
            } else if (checksums.operation === 'extract') {
                checksumHtml += `<div class="mb-2">
                    <strong>Original PDF:</strong>
                    <span class="text-monospace text-break">${checksums.original}</span>
                </div>`;

                checksumHtml += `<div class="mb-2">
                    <strong>Clean PDF:</strong>
                    <span class="text-monospace text-break">${checksums.cleanPdf}</span>
                </div>`;

                checksumHtml += `<div class="mb-2">
                    <strong>ZIP Archive:</strong>
                    <span class="text-monospace text-break">${checksums.zip}</span>
                </div>`;

                if (checksums.extracted) {
                    checksumHtml += `<div class="mb-2"><strong>Extracted Files:</strong></div>`;
                    checksumHtml += `<ul class="list-unstyled ms-3">`;

                    for (const [filename, checksum] of Object.entries(checksums.extracted)) {
                        checksumHtml += `<li class="mb-1">
                            <i class="fas fa-file-alt me-1"></i> <strong>${filename}:</strong>
                            <span class="text-monospace text-break">${checksum}</span>
                        </li>`;
                    }

                    checksumHtml += `</ul>`;
                }
            }

            checksumContent.innerHTML = checksumHtml;
            checksumDetails.classList.remove('d-none');
        } else {
            // Hide checksum details if no checksums provided
            checksumContent.innerHTML = '';
            checksumDetails.classList.add('d-none');
        }

        status.classList.add('fade-in');
        status.classList.remove('d-none');
    }

    // Store selected files for multiple file inputs
    const selectedFilesMap = new Map();

    // Drag and drop functionality
    function setupDropZone(dropZone, input, previewElement, isMultiple = false) {
        // Initialize the selected files map for this input
        if (isMultiple) {
            selectedFilesMap.set(input.id, []);
        }

        // Prevent default drag behaviors
        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
            dropZone.addEventListener(eventName, preventDefaults, false);
            document.body.addEventListener(eventName, preventDefaults, false);
        });

        // Highlight drop zone when item is dragged over it
        ['dragenter', 'dragover'].forEach(eventName => {
            dropZone.addEventListener(eventName, highlight, false);
        });

        ['dragleave', 'drop'].forEach(eventName => {
            dropZone.addEventListener(eventName, unhighlight, false);
        });

        // Handle dropped files
        dropZone.addEventListener('drop', function(e) {
            const dt = e.dataTransfer;
            const droppedFiles = dt.files;

            if (isMultiple) {
                // For multiple files input, append to existing files
                const currentFiles = selectedFilesMap.get(input.id) || [];

                // Convert FileList to array and append new files
                for (let i = 0; i < droppedFiles.length; i++) {
                    // Check if file already exists by name (simple duplicate check)
                    const isDuplicate = currentFiles.some(f => f.name === droppedFiles[i].name);
                    if (!isDuplicate) {
                        currentFiles.push(droppedFiles[i]);
                    }
                }

                // Update the selected files
                selectedFilesMap.set(input.id, currentFiles);

                // Update the input's files property using a DataTransfer object
                updateInputFiles(input, currentFiles);

                // Update the preview
                updateFilesPreview(currentFiles, previewElement, input.id);
            } else {
                // For single file input
                if (droppedFiles.length > 0) {
                    input.files = droppedFiles;
                    updateFilePreview(droppedFiles[0], previewElement);
                }
            }
        }, false);

        // Handle file input change
        input.addEventListener('change', function(e) {
            if (isMultiple && !e.skipProcessing) {
                // Get current selected files
                const currentFiles = selectedFilesMap.get(input.id) || [];

                // Add newly selected files
                for (let i = 0; i < this.files.length; i++) {
                    // Check for duplicates
                    const isDuplicate = currentFiles.some(f => f.name === this.files[i].name);
                    if (!isDuplicate) {
                        currentFiles.push(this.files[i]);
                    }
                }

                // Update the selected files
                selectedFilesMap.set(input.id, currentFiles);

                // Update the input's files property
                updateInputFiles(input, currentFiles);

                // Update the preview
                updateFilesPreview(currentFiles, previewElement, input.id);
            } else if (!isMultiple) {
                if (this.files.length > 0) {
                    updateFilePreview(this.files[0], previewElement);
                }
            }
        });

        // Handle click on drop zone
        dropZone.addEventListener('click', function(e) {
            // Prevent the click from reaching the input if it's a direct click on the drop zone
            if (e.target !== input) {
                e.preventDefault();
                e.stopPropagation();
                input.click();
            }
        });
    }

    // Helper function to update a file input's files property
    function updateInputFiles(input, filesArray) {
        // Create a DataTransfer object
        const dataTransfer = new DataTransfer();

        // Add files to the DataTransfer object
        filesArray.forEach(file => {
            dataTransfer.items.add(file);
        });

        // Set the input's files property
        input.files = dataTransfer.files;

        // Dispatch a change event with a flag to avoid infinite loops
        const event = new Event('change', { bubbles: true });
        event.skipProcessing = true;
        input.dispatchEvent(event);
    }

    function preventDefaults(e) {
        e.preventDefault();
        e.stopPropagation();
    }

    function highlight(e) {
        this.classList.add('drop-zone--over');
    }

    function unhighlight(e) {
        this.classList.remove('drop-zone--over');
    }

    function updateFilePreview(file, previewElement) {
        // Get the file name element
        const fileNameElement = previewElement.querySelector('.file-name');
        fileNameElement.textContent = file.name;

        // Add file size information if it doesn't exist
        let fileSizeElement = previewElement.querySelector('.file-size');
        if (!fileSizeElement) {
            fileSizeElement = document.createElement('small');
            fileSizeElement.className = 'text-muted ms-2 file-size';
            fileNameElement.parentNode.insertBefore(fileSizeElement, fileNameElement.nextSibling);
        }

        // Update file size
        fileSizeElement.textContent = `(${formatFileSize(file.size)})`;

        // Show the preview
        previewElement.classList.remove('d-none');

        // Add event listener to remove button
        const removeButton = previewElement.querySelector('.remove-file');
        removeButton.addEventListener('click', function() {
            // Clear the file input
            const fileInput = previewElement.previousElementSibling.querySelector('input[type="file"]');
            fileInput.value = '';
            // Hide the preview
            previewElement.classList.add('d-none');
        });
    }

    function updateFilesPreview(files, previewElement, inputId) {
        const filesList = previewElement.querySelector('.files-list');
        filesList.innerHTML = '';

        if (files.length > 0) {
            for (let i = 0; i < files.length; i++) {
                const file = files[i];
                const fileElement = document.createElement('div');
                fileElement.className = 'd-flex align-items-center';
                fileElement.dataset.fileName = file.name; // Store filename for removal

                // Determine icon based on file type
                let iconClass = 'fa-file';
                if (file.type.includes('pdf')) {
                    iconClass = 'fa-file-pdf';
                } else if (file.type.includes('image')) {
                    iconClass = 'fa-file-image';
                } else if (file.type.includes('text')) {
                    iconClass = 'fa-file-alt';
                } else if (file.type.includes('video')) {
                    iconClass = 'fa-file-video';
                } else if (file.type.includes('audio')) {
                    iconClass = 'fa-file-audio';
                } else if (file.type.includes('zip') || file.type.includes('rar') || file.type.includes('archive')) {
                    iconClass = 'fa-file-archive';
                }

                fileElement.innerHTML = `
                    <i class="fas ${iconClass} text-primary me-2 fs-5"></i>
                    <span class="file-name">${file.name}</span>
                    <small class="text-muted ms-2">(${formatFileSize(file.size)})</small>
                    <button type="button" class="btn-close ms-auto remove-file" aria-label="Remove file"></button>
                `;

                filesList.appendChild(fileElement);

                // Add event listener to the remove button
                const removeButton = fileElement.querySelector('.remove-file');
                removeButton.addEventListener('click', function() {
                    // Get the file name from the parent element
                    const fileName = fileElement.dataset.fileName;

                    // Get the current files array
                    const currentFiles = selectedFilesMap.get(inputId) || [];

                    // Remove the file from the array
                    const updatedFiles = currentFiles.filter(f => f.name !== fileName);

                    // Update the selected files
                    selectedFilesMap.set(inputId, updatedFiles);

                    // Update the input's files property
                    const input = document.getElementById(inputId);
                    updateInputFiles(input, updatedFiles);

                    // Update the preview
                    updateFilesPreview(updatedFiles, previewElement, inputId);
                });
            }
            previewElement.classList.remove('d-none');
        } else {
            previewElement.classList.add('d-none');
        }
    }

    function formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    // Initialize drop zones
    setupDropZone(mainPdfDropZone, mainPdfInput, mainPdfPreview);
    setupDropZone(embeddedFilesDropZone, embeddedFilesInput, embeddedFilesPreview, true);
    setupDropZone(pdfFileDropZone, pdfFileInput, pdfFilePreview);

    // Handle embed form submission
    embedForm.addEventListener('submit', async function(e) {
        e.preventDefault();

        if (!mainPdfInput.files.length) {
            showStatus('Please select a main PDF file', false, true);
            return;
        }

        if (!embeddedFilesInput.files.length) {
            showStatus('Please select at least one file to embed', false, true);
            return;
        }

        const formData = new FormData();
        formData.append('main_pdf', mainPdfInput.files[0]);

        for (let i = 0; i < embeddedFilesInput.files.length; i++) {
            formData.append('embedded_pdfs', embeddedFilesInput.files[i]);
        }

        showStatus('Creating embedded PDF... Please wait');

        try {
            const response = await fetch('/create_embedded_pdf', {
                method: 'POST',
                body: formData
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.detail || 'Error creating embedded PDF');
            }

            // Extract checksums from headers
            const checksums = {
                operation: 'embed',
                main: response.headers.get('X-Checksum-Main'),
                result: response.headers.get('X-Checksum-Result')
            };

            // Parse embedded checksums if available
            const embeddedChecksums = response.headers.get('X-Checksum-Embedded');
            if (embeddedChecksums) {
                try {
                    checksums.embedded = JSON.parse(embeddedChecksums);
                } catch (e) {
                    console.error('Error parsing embedded checksums:', e);
                }
            }

            // Create blob URL for the download button
            const blob = await response.blob();
            const url = window.URL.createObjectURL(blob);

            // Create download info object
            const downloadInfo = {
                url: url,
                filename: 'embedded.pdf',
                fileType: 'pdf'
            };

            showStatus('Embedded PDF created successfully!', false, false, checksums, downloadInfo);

            // Reset the form
            embedForm.reset();

            // Clear the selected files map for embedded files
            selectedFilesMap.set('embeddedFiles', []);

            // Hide previews
            mainPdfPreview.classList.add('d-none');
            embeddedFilesPreview.classList.add('d-none');

        } catch (error) {
            console.error('Error:', error);
            showStatus(`Error: ${error.message}`, false, true);
        }
    });

    // Handle extract form submission
    extractForm.addEventListener('submit', async function(e) {
        e.preventDefault();

        if (!pdfFileInput.files.length) {
            showStatus('Please select a PDF file with attachments', false, true);
            return;
        }

        const formData = new FormData();
        formData.append('pdf_file', pdfFileInput.files[0]);

        showStatus('Extracting attachments... Please wait');

        try {
            const response = await fetch('/extract_embedded_pdf', {
                method: 'POST',
                body: formData
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.detail || 'Error extracting attachments');
            }

            // Extract checksums from headers
            const checksums = {
                operation: 'extract',
                original: response.headers.get('X-Checksum-Original'),
                cleanPdf: response.headers.get('X-Checksum-Clean-PDF'),
                zip: response.headers.get('X-Checksum-Zip')
            };

            // Parse extracted checksums if available
            const extractedChecksums = response.headers.get('X-Checksum-Extracted');
            if (extractedChecksums) {
                try {
                    checksums.extracted = JSON.parse(extractedChecksums);
                } catch (e) {
                    console.error('Error parsing extracted checksums:', e);
                }
            }

            // Create blob URL for the download button
            const blob = await response.blob();
            const url = window.URL.createObjectURL(blob);

            // Create download info object
            const downloadInfo = {
                url: url,
                filename: 'extracted_attachments.zip',
                fileType: 'zip'
            };

            showStatus('Attachments extracted successfully!', false, false, checksums, downloadInfo);

            // Reset the form
            extractForm.reset();

            // Hide preview
            pdfFilePreview.classList.add('d-none');

        } catch (error) {
            console.error('Error:', error);
            showStatus(`Error: ${error.message}`, false, true);
        }
    });
});
